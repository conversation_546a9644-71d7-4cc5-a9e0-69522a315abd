# نظام التعرف على لغة الإشارة العربية
## Arabic Sign Language Recognition System

### 📋 وصف المشروع

هذا المشروع عبارة عن نظام متقدم للتعرف على لغة الإشارة العربية باستخدام تقنيات الذكاء الاصطناعي والرؤية الحاسوبية. يمكن للنظام:

- جمع البيانات للحروف العربية
- تدريب نموذج التعرف على الإشارات
- التعرف على الإشارات في الوقت الفعلي
- عرض النتائج في واجهة ويب تفاعلية

### 🛠️ المتطلبات

- Python 3.8 أو أحدث
- كاميرا ويب
- متصفح ويب حديث

### 📦 التثبيت

1. **استنساخ المشروع أو تحميل الملفات**

2. **تثبيت المكتبات المطلوبة:**
```bash
pip install -r requirements_arabic_model.txt
```

3. **التأكد من وجود كاميرا ويب متصلة بالجهاز**

### 🚀 تشغيل النظام

1. **تشغيل الخادم:**
```bash
python model2video.py
```

2. **فتح المتصفح والانتقال إلى:**
```
http://localhost:5000
```

### 📖 كيفية الاستخدام

#### 1. جمع البيانات
- اختر حرفاً عربياً من الشبكة
- حدد عدد العينات المطلوبة (افتراضي: 100)
- انقر على "بدء جمع البيانات"
- قم بأداء الإشارة أمام الكاميرا
- كرر العملية لجميع الحروف المطلوبة

#### 2. تدريب النموذج
- بعد جمع البيانات لعدة حروف
- انقر على "تدريب النموذج"
- انتظر حتى اكتمال التدريب

#### 3. التعرف على الإشارات
- انقر على "بدء التعرف"
- قم بأداء الإشارات أمام الكاميرا
- ستظهر النتائج في الوقت الفعلي

### 🎯 الميزات

#### ✨ الواجهة الرئيسية
- **تصميم عربي متجاوب:** واجهة مصممة خصيصاً للغة العربية
- **عرض الحالة:** مراقبة حالة النظام في الوقت الفعلي
- **نتائج فورية:** عرض الحروف المتعرف عليها مع نسبة الثقة

#### 🔧 لوحة التحكم
- **جمع البيانات:** أدوات سهلة لجمع بيانات التدريب
- **تدريب النموذج:** تدريب النموذج بنقرة واحدة
- **التعرف المباشر:** بدء وإيقاف التعرف على الإشارات

#### 📊 المراقبة والتحليل
- **حالة الاتصال:** مراقبة اتصال الخادم
- **إحصائيات التدريب:** عرض تقدم جمع البيانات
- **دقة التعرف:** عرض نسبة الثقة للتنبؤات

### 🏗️ البنية التقنية

#### Backend (Python Flask)
- **Flask:** إطار عمل الويب
- **SocketIO:** الاتصال في الوقت الفعلي
- **MediaPipe:** اكتشاف وتتبع اليد
- **scikit-learn:** تدريب نموذج التصنيف
- **OpenCV:** معالجة الصور والفيديو

#### Frontend (HTML/CSS/JavaScript)
- **Bootstrap 5:** تصميم متجاوب
- **Socket.IO Client:** الاتصال مع الخادم
- **Font Awesome:** الأيقونات
- **Cairo Font:** خط عربي جميل

#### AI/ML Pipeline
1. **اكتشاف اليد:** MediaPipe Hands
2. **استخراج النقاط:** 21 نقطة مرجعية
3. **تطبيع البيانات:** تحويل الإحداثيات النسبية
4. **التصنيف:** Random Forest Classifier
5. **التنبؤ:** نتائج مع نسبة الثقة

### 📁 هيكل الملفات

```
arabic-sign-recognition/
├── model2video.py              # الملف الرئيسي للخادم
├── templates/
│   └── arabic_sign_recognition.html  # واجهة الويب
├── requirements_arabic_model.txt     # المكتبات المطلوبة
├── README_Arabic_Model.md           # دليل الاستخدام
└── arabic_sign_model.pkl           # النموذج المدرب (يتم إنشاؤه تلقائياً)
```

### 🎬 إنشاء فيديو توضيحي

لإنشاء فيديو توضيحي للنظام:

1. **تشغيل النظام كما هو موضح أعلاه**
2. **استخدام برنامج تسجيل الشاشة مثل:**
   - OBS Studio (مجاني)
   - Camtasia
   - Bandicam

3. **سيناريو الفيديو المقترح:**
   - عرض الواجهة الرئيسية
   - شرح عملية جمع البيانات
   - توضيح تدريب النموذج
   - عرض التعرف على الإشارات في الوقت الفعلي
   - إظهار دقة النتائج

### 🔧 استكشاف الأخطاء

#### مشاكل شائعة وحلولها:

**1. خطأ في الكاميرا:**
```
- تأكد من توصيل الكاميرا
- أغلق التطبيقات الأخرى التي تستخدم الكاميرا
- جرب تغيير رقم الكاميرا في الكود (0, 1, 2...)
```

**2. خطأ في تثبيت المكتبات:**
```bash
# جرب تحديث pip أولاً
python -m pip install --upgrade pip

# ثم أعد تثبيت المكتبات
pip install -r requirements_arabic_model.txt
```

**3. مشاكل في الأداء:**
```
- تأكد من وجود إضاءة كافية
- تأكد من وضوح اليد في الكاميرا
- قلل عدد التطبيقات المفتوحة
```

### 📈 تحسينات مستقبلية

- **دعم المزيد من الحروف والكلمات**
- **تحسين دقة النموذج**
- **إضافة دعم للإشارات ثنائية اليد**
- **تطوير تطبيق موبايل**
- **إضافة ميزة حفظ وتحميل النماذج**

### 🤝 المساهمة

نرحب بالمساهمات! يمكنك:
- الإبلاغ عن الأخطاء
- اقتراح ميزات جديدة
- تحسين الكود
- إضافة دعم لحروف أو كلمات جديدة

### 📞 الدعم

إذا واجهت أي مشاكل أو لديك أسئلة، يمكنك:
- فتح issue في المشروع
- مراجعة قسم استكشاف الأخطاء
- التحقق من متطلبات النظام

---

**ملاحظة:** هذا المشروع مصمم لأغراض تعليمية وبحثية. للاستخدام التجاري، يرجى مراجعة تراخيص المكتبات المستخدمة.
