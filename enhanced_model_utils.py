#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
أدوات محسنة لنموذج التعرف على لغة الإشارة العربية
Enhanced utilities for Arabic Sign Language Recognition Model
"""

import cv2
import numpy as np
import mediapipe as mp
import pickle
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
import json

class EnhancedArabicSignModel:
    def __init__(self):
        self.hands = mp.solutions.hands.Hands(
            static_image_mode=False,
            max_num_hands=1,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.model = None
        self.data_history = []
        self.training_history = []
        self.arabic_letters = [
            'أ', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص', 'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'
        ]
        
    def extract_enhanced_features(self, landmarks):
        """استخراج ميزات محسنة من النقاط المرجعية"""
        if landmarks is None:
            return None
            
        features = []
        
        # 1. الإحداثيات الأساسية (مطبعة)
        wrist = landmarks[:3]  # نقطة المعصم
        for i in range(0, len(landmarks), 3):
            x, y, z = landmarks[i:i+3]
            # إحداثيات نسبية للمعصم
            rel_x = x - wrist[0]
            rel_y = y - wrist[1] 
            rel_z = z - wrist[2]
            features.extend([rel_x, rel_y, rel_z])
        
        # 2. المسافات بين النقاط المهمة
        # مسافة الإبهام إلى باقي الأصابع
        thumb_tip = landmarks[12:15]  # طرف الإبهام
        index_tip = landmarks[24:27]  # طرف السبابة
        middle_tip = landmarks[36:39]  # طرف الوسطى
        ring_tip = landmarks[48:51]   # طرف البنصر
        pinky_tip = landmarks[60:63]  # طرف الخنصر
        
        distances = [
            self._calculate_distance(thumb_tip, index_tip),
            self._calculate_distance(thumb_tip, middle_tip),
            self._calculate_distance(thumb_tip, ring_tip),
            self._calculate_distance(thumb_tip, pinky_tip),
            self._calculate_distance(index_tip, middle_tip),
            self._calculate_distance(middle_tip, ring_tip),
            self._calculate_distance(ring_tip, pinky_tip)
        ]
        features.extend(distances)
        
        # 3. الزوايا بين المفاصل
        angles = self._calculate_finger_angles(landmarks)
        features.extend(angles)
        
        # 4. نسب الأصابع (مفتوحة/مغلقة)
        finger_ratios = self._calculate_finger_ratios(landmarks)
        features.extend(finger_ratios)
        
        return np.array(features)
    
    def _calculate_distance(self, point1, point2):
        """حساب المسافة الإقليدية بين نقطتين"""
        return np.sqrt(sum((a - b) ** 2 for a, b in zip(point1, point2)))
    
    def _calculate_finger_angles(self, landmarks):
        """حساب زوايا الأصابع"""
        angles = []
        
        # تعريف نقاط المفاصل لكل إصبع
        finger_joints = [
            [0, 3, 6, 9, 12],    # الإبهام
            [0, 15, 18, 21, 24], # السبابة
            [0, 27, 30, 33, 36], # الوسطى
            [0, 39, 42, 45, 48], # البنصر
            [0, 51, 54, 57, 60]  # الخنصر
        ]
        
        for joints in finger_joints:
            for i in range(len(joints) - 2):
                p1 = landmarks[joints[i]*3:(joints[i]*3)+3]
                p2 = landmarks[joints[i+1]*3:(joints[i+1]*3)+3]
                p3 = landmarks[joints[i+2]*3:(joints[i+2]*3)+3]
                
                angle = self._calculate_angle(p1, p2, p3)
                angles.append(angle)
        
        return angles
    
    def _calculate_angle(self, p1, p2, p3):
        """حساب الزاوية بين ثلاث نقاط"""
        v1 = np.array(p1) - np.array(p2)
        v2 = np.array(p3) - np.array(p2)
        
        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
        cos_angle = np.clip(cos_angle, -1.0, 1.0)
        angle = np.arccos(cos_angle)
        
        return np.degrees(angle)
    
    def _calculate_finger_ratios(self, landmarks):
        """حساب نسب انثناء الأصابع"""
        ratios = []
        
        # تعريف نقاط الأطراف والقواعد
        finger_tips = [12, 24, 36, 48, 60]  # أطراف الأصابع
        finger_bases = [3, 15, 27, 39, 51]  # قواعد الأصابع
        palm_center = 0  # مركز الكف (المعصم)
        
        for tip, base in zip(finger_tips, finger_bases):
            tip_point = landmarks[tip*3:(tip*3)+3]
            base_point = landmarks[base*3:(base*3)+3]
            palm_point = landmarks[palm_center*3:(palm_center*3)+3]
            
            # نسبة المسافة من الطرف إلى القاعدة مقارنة بالمسافة من القاعدة إلى المعصم
            tip_to_base = self._calculate_distance(tip_point, base_point)
            base_to_palm = self._calculate_distance(base_point, palm_point)
            
            ratio = tip_to_base / (base_to_palm + 1e-6)  # تجنب القسمة على صفر
            ratios.append(ratio)
        
        return ratios
    
    def collect_enhanced_dataset(self, letter, num_samples=200, save_path="dataset"):
        """جمع مجموعة بيانات محسنة"""
        if not os.path.exists(save_path):
            os.makedirs(save_path)
        
        print(f"🔄 بدء جمع البيانات المحسنة للحرف: {letter}")
        
        cap = cv2.VideoCapture(0)
        collected_samples = []
        sample_count = 0
        
        # إعدادات التنويع
        brightness_variations = [0.8, 1.0, 1.2]
        current_brightness = 0
        
        while sample_count < num_samples:
            ret, frame = cap.read()
            if not ret:
                continue
            
            # تطبيق تنويع في السطوع
            brightness = brightness_variations[current_brightness % len(brightness_variations)]
            frame = cv2.convertScaleAbs(frame, alpha=brightness, beta=0)
            
            frame = cv2.flip(frame, 1)
            
            # استخراج النقاط المرجعية
            image_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.hands.process(image_rgb)
            
            if results.multi_hand_landmarks:
                for hand_landmarks in results.multi_hand_landmarks:
                    # استخراج الإحداثيات
                    landmarks = []
                    for landmark in hand_landmarks.landmark:
                        landmarks.extend([landmark.x, landmark.y, landmark.z])
                    
                    # استخراج الميزات المحسنة
                    enhanced_features = self.extract_enhanced_features(landmarks)
                    
                    if enhanced_features is not None:
                        sample_data = {
                            'features': enhanced_features,
                            'letter': letter,
                            'timestamp': datetime.now().isoformat(),
                            'brightness': brightness
                        }
                        
                        collected_samples.append(sample_data)
                        sample_count += 1
                        current_brightness += 1
                        
                        # رسم النقاط المرجعية
                        mp.solutions.drawing_utils.draw_landmarks(
                            frame, hand_landmarks, mp.solutions.hands.HAND_CONNECTIONS)
            
            # عرض المعلومات
            cv2.putText(frame, f"Letter: {letter}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, f"Samples: {sample_count}/{num_samples}", (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, f"Brightness: {brightness:.1f}", (10, 110), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
            
            cv2.imshow('Enhanced Data Collection', frame)
            
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        cap.release()
        cv2.destroyAllWindows()
        
        # حفظ البيانات
        filename = f"{save_path}/{letter}_enhanced_data.pkl"
        with open(filename, 'wb') as f:
            pickle.dump(collected_samples, f)
        
        print(f"✅ تم حفظ {len(collected_samples)} عينة في {filename}")
        return collected_samples
    
    def train_enhanced_model(self, dataset_path="dataset"):
        """تدريب نموذج محسن"""
        print("🧠 بدء تدريب النموذج المحسن...")
        
        # تحميل جميع البيانات
        all_features = []
        all_labels = []
        
        for letter in self.arabic_letters:
            filename = f"{dataset_path}/{letter}_enhanced_data.pkl"
            if os.path.exists(filename):
                with open(filename, 'rb') as f:
                    data = pickle.load(f)
                    for sample in data:
                        all_features.append(sample['features'])
                        all_labels.append(sample['letter'])
                print(f"✅ تم تحميل بيانات الحرف {letter}: {len(data)} عينة")
        
        if not all_features:
            print("❌ لا توجد بيانات للتدريب")
            return False
        
        X = np.array(all_features)
        y = np.array(all_labels)
        
        print(f"📊 إجمالي البيانات: {len(X)} عينة، {len(np.unique(y))} حرف")
        
        # تقسيم البيانات
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # تدريب نموذج محسن
        self.model = RandomForestClassifier(
            n_estimators=200,
            max_depth=20,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1
        )
        
        self.model.fit(X_train, y_train)
        
        # تقييم النموذج
        y_pred = self.model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        # التحقق المتقاطع
        cv_scores = cross_val_score(self.model, X_train, y_train, cv=5)
        
        print(f"✅ دقة النموذج: {accuracy:.2%}")
        print(f"📈 متوسط التحقق المتقاطع: {cv_scores.mean():.2%} (±{cv_scores.std()*2:.2%})")
        
        # حفظ تقرير التدريب
        training_report = {
            'timestamp': datetime.now().isoformat(),
            'accuracy': accuracy,
            'cv_mean': cv_scores.mean(),
            'cv_std': cv_scores.std(),
            'num_samples': len(X),
            'num_classes': len(np.unique(y)),
            'feature_importance': self.model.feature_importances_.tolist()
        }
        
        self.training_history.append(training_report)
        
        # حفظ النموذج
        with open('enhanced_arabic_sign_model.pkl', 'wb') as f:
            pickle.dump(self.model, f)
        
        # حفظ تاريخ التدريب
        with open('training_history.json', 'w', encoding='utf-8') as f:
            json.dump(self.training_history, f, ensure_ascii=False, indent=2)
        
        # إنشاء تقرير مفصل
        self._generate_detailed_report(y_test, y_pred, accuracy)
        
        return True
    
    def _generate_detailed_report(self, y_test, y_pred, accuracy):
        """إنشاء تقرير مفصل عن أداء النموذج"""
        print("\n📋 تقرير مفصل عن أداء النموذج:")
        print("=" * 50)
        
        # تقرير التصنيف
        report = classification_report(y_test, y_pred, output_dict=True)
        
        print(f"الدقة الإجمالية: {accuracy:.2%}")
        print(f"عدد العينات المختبرة: {len(y_test)}")
        print(f"عدد الحروف: {len(np.unique(y_test))}")
        
        print("\nأداء كل حرف:")
        for letter in sorted(np.unique(y_test)):
            if letter in report:
                precision = report[letter]['precision']
                recall = report[letter]['recall']
                f1 = report[letter]['f1-score']
                support = report[letter]['support']
                print(f"  {letter}: دقة={precision:.2%}, استدعاء={recall:.2%}, F1={f1:.2%}, عينات={support}")
        
        # حفظ مصفوفة الارتباك
        try:
            cm = confusion_matrix(y_test, y_pred)
            plt.figure(figsize=(12, 10))
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                       xticklabels=sorted(np.unique(y_test)),
                       yticklabels=sorted(np.unique(y_test)))
            plt.title('مصفوفة الارتباك - نموذج التعرف على لغة الإشارة العربية')
            plt.xlabel('التنبؤ')
            plt.ylabel('الحقيقة')
            plt.tight_layout()
            plt.savefig('confusion_matrix.png', dpi=300, bbox_inches='tight')
            plt.close()
            print("\n💾 تم حفظ مصفوفة الارتباك في confusion_matrix.png")
        except Exception as e:
            print(f"⚠️ خطأ في إنشاء مصفوفة الارتباك: {e}")
    
    def load_enhanced_model(self):
        """تحميل النموذج المحسن"""
        try:
            with open('enhanced_arabic_sign_model.pkl', 'rb') as f:
                self.model = pickle.load(f)
            print("✅ تم تحميل النموذج المحسن")
            return True
        except FileNotFoundError:
            print("⚠️ لم يتم العثور على النموذج المحسن")
            return False
    
    def predict_with_confidence(self, landmarks):
        """التنبؤ مع حساب الثقة المحسن"""
        if self.model is None:
            return None, 0.0, {}
        
        features = self.extract_enhanced_features(landmarks)
        if features is None:
            return None, 0.0, {}
        
        # التنبؤ
        prediction = self.model.predict([features])[0]
        probabilities = self.model.predict_proba([features])[0]
        
        # حساب الثقة
        max_prob = np.max(probabilities)
        
        # إحصائيات إضافية
        sorted_indices = np.argsort(probabilities)[::-1]
        top_3_predictions = []
        
        for i in range(min(3, len(sorted_indices))):
            idx = sorted_indices[i]
            letter = self.model.classes_[idx]
            prob = probabilities[idx]
            top_3_predictions.append({'letter': letter, 'probability': prob})
        
        stats = {
            'top_3': top_3_predictions,
            'entropy': -np.sum(probabilities * np.log(probabilities + 1e-10)),
            'max_prob': max_prob,
            'prediction_strength': max_prob - np.partition(probabilities, -2)[-2]
        }
        
        return prediction, max_prob, stats

# مثال على الاستخدام
if __name__ == "__main__":
    model = EnhancedArabicSignModel()
    
    print("🚀 نموذج التعرف على لغة الإشارة العربية المحسن")
    print("=" * 50)
    
    while True:
        print("\nاختر عملية:")
        print("1. جمع البيانات لحرف معين")
        print("2. تدريب النموذج المحسن")
        print("3. تحميل النموذج المحسن")
        print("4. اختبار التعرف")
        print("5. خروج")
        
        choice = input("اختيارك: ").strip()
        
        if choice == "1":
            letter = input("أدخل الحرف العربي: ").strip()
            if letter in model.arabic_letters:
                num_samples = int(input("عدد العينات (افتراضي 200): ") or "200")
                model.collect_enhanced_dataset(letter, num_samples)
            else:
                print("❌ حرف غير صحيح")
        
        elif choice == "2":
            model.train_enhanced_model()
        
        elif choice == "3":
            model.load_enhanced_model()
        
        elif choice == "4":
            if model.model is None:
                print("❌ يجب تدريب أو تحميل النموذج أولاً")
                continue
            
            print("🎥 بدء اختبار التعرف (اضغط 'q' للخروج)")
            cap = cv2.VideoCapture(0)
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    continue
                
                frame = cv2.flip(frame, 1)
                
                # استخراج النقاط المرجعية
                image_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                results = model.hands.process(image_rgb)
                
                if results.multi_hand_landmarks:
                    for hand_landmarks in results.multi_hand_landmarks:
                        # استخراج الإحداثيات
                        landmarks = []
                        for landmark in hand_landmarks.landmark:
                            landmarks.extend([landmark.x, landmark.y, landmark.z])
                        
                        # التنبؤ
                        prediction, confidence, stats = model.predict_with_confidence(landmarks)
                        
                        if prediction and confidence > 0.5:
                            # عرض النتيجة
                            cv2.putText(frame, f"Letter: {prediction}", (10, 30),
                                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                            cv2.putText(frame, f"Confidence: {confidence:.2%}", (10, 70),
                                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                            
                            # عرض أفضل 3 تنبؤات
                            y_offset = 110
                            for i, pred in enumerate(stats['top_3'][:3]):
                                text = f"{i+1}. {pred['letter']}: {pred['probability']:.1%}"
                                cv2.putText(frame, text, (10, y_offset),
                                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 1)
                                y_offset += 25
                        
                        # رسم النقاط المرجعية
                        mp.solutions.drawing_utils.draw_landmarks(
                            frame, hand_landmarks, mp.solutions.hands.HAND_CONNECTIONS)
                
                cv2.imshow('Enhanced Arabic Sign Recognition', frame)
                
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
            
            cap.release()
            cv2.destroyAllWindows()
        
        elif choice == "5":
            print("👋 وداعاً!")
            break
        
        else:
            print("❌ اختيار غير صحيح")
