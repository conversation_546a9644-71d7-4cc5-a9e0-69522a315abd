<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام التعرف على لغة الإشارة العربية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            padding: 30px;
            max-width: 1200px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            color: #2c3e50;
        }
        
        .header h1 {
            font-weight: 700;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            color: #7f8c8d;
        }
        
        .control-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border: 2px solid #e9ecef;
        }
        
        .status-card {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .prediction-display {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            min-height: 150px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .prediction-letter {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .prediction-confidence {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .letters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .letter-btn {
            background: #ecf0f1;
            border: 2px solid #bdc3c7;
            border-radius: 10px;
            padding: 15px;
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .letter-btn:hover {
            background: #3498db;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        .letter-btn.selected {
            background: #e74c3c;
            color: white;
            border-color: #c0392b;
        }
        
        .btn-custom {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn-primary-custom {
            background: linear-gradient(45deg, #3498db, #2980b9);
            border: none;
            color: white;
        }
        
        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn-success-custom {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            border: none;
            color: white;
        }
        
        .btn-success-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }
        
        .btn-danger-custom {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            border: none;
            color: white;
        }
        
        .btn-danger-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }
        
        .progress-custom {
            height: 25px;
            border-radius: 15px;
            background: #ecf0f1;
            margin: 15px 0;
        }
        
        .progress-bar-custom {
            background: linear-gradient(45deg, #3498db, #2980b9);
            border-radius: 15px;
            transition: width 0.3s ease;
        }
        
        .alert-custom {
            border-radius: 15px;
            border: none;
            padding: 15px 20px;
            margin: 10px 0;
        }
        
        .loading-spinner {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .spinner-border-custom {
            width: 3rem;
            height: 3rem;
            border-width: 0.3em;
        }
        
        .connection-status {
            position: fixed;
            top: 20px;
            left: 20px;
            padding: 10px 15px;
            border-radius: 25px;
            font-weight: bold;
            z-index: 1000;
        }
        
        .connected {
            background: #27ae60;
            color: white;
        }
        
        .disconnected {
            background: #e74c3c;
            color: white;
        }
        
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .prediction-letter {
                font-size: 3rem;
            }
            
            .letters-grid {
                grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">
        <i class="fas fa-circle"></i> غير متصل
    </div>

    <div class="container">
        <div class="main-container">
            <!-- Header -->
            <div class="header">
                <h1><i class="fas fa-hands"></i> نظام التعرف على لغة الإشارة العربية</h1>
                <p>تقنية متقدمة للتعرف على الحروف العربية بلغة الإشارة</p>
            </div>

            <!-- Status Card -->
            <div class="status-card">
                <h4><i class="fas fa-info-circle"></i> حالة النظام</h4>
                <div class="row mt-3">
                    <div class="col-md-3">
                        <strong>النموذج:</strong>
                        <span id="modelStatus">غير مدرب</span>
                    </div>
                    <div class="col-md-3">
                        <strong>جمع البيانات:</strong>
                        <span id="collectingStatus">متوقف</span>
                    </div>
                    <div class="col-md-3">
                        <strong>التعرف:</strong>
                        <span id="recognitionStatus">متوقف</span>
                    </div>
                    <div class="col-md-3">
                        <strong>الاتصال:</strong>
                        <span id="connectionStatusText">غير متصل</span>
                    </div>
                </div>
            </div>

            <!-- Prediction Display -->
            <div class="prediction-display">
                <div class="prediction-letter" id="predictionLetter">-</div>
                <div class="prediction-confidence" id="predictionConfidence">في انتظار التعرف...</div>
            </div>

            <!-- Control Panel -->
            <div class="control-panel">
                <h5><i class="fas fa-cogs"></i> لوحة التحكم</h5>
                
                <!-- Data Collection Section -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h6><i class="fas fa-database"></i> جمع البيانات</h6>
                        <p>اختر حرفاً لجمع البيانات الخاصة به:</p>
                        
                        <div class="letters-grid" id="lettersGrid">
                            <!-- سيتم ملء الحروف هنا بواسطة JavaScript -->
                        </div>
                        
                        <div class="mt-3">
                            <label for="numSamples" class="form-label">عدد العينات:</label>
                            <input type="number" class="form-control" id="numSamples" value="100" min="10" max="500">
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-primary-custom btn-custom" onclick="startDataCollection()">
                                <i class="fas fa-play"></i> بدء جمع البيانات
                            </button>
                            <button class="btn btn-danger-custom btn-custom" onclick="stopDataCollection()">
                                <i class="fas fa-stop"></i> إيقاف جمع البيانات
                            </button>
                        </div>
                        
                        <div class="progress-custom" style="display: none;" id="collectionProgress">
                            <div class="progress-bar progress-bar-custom" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- Model Training Section -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h6><i class="fas fa-brain"></i> تدريب النموذج</h6>
                        <p>بعد جمع البيانات، قم بتدريب النموذج:</p>
                        
                        <button class="btn btn-success-custom btn-custom" onclick="trainModel()">
                            <i class="fas fa-graduation-cap"></i> تدريب النموذج
                        </button>
                    </div>
                </div>

                <!-- Recognition Section -->
                <div class="row">
                    <div class="col-md-12">
                        <h6><i class="fas fa-eye"></i> التعرف على الإشارات</h6>
                        <p>ابدأ التعرف على الإشارات في الوقت الفعلي:</p>
                        
                        <button class="btn btn-primary-custom btn-custom" onclick="startRecognition()">
                            <i class="fas fa-video"></i> بدء التعرف
                        </button>
                        <button class="btn btn-danger-custom btn-custom" onclick="stopRecognition()">
                            <i class="fas fa-video-slash"></i> إيقاف التعرف
                        </button>
                    </div>
                </div>
            </div>

            <!-- Loading Spinner -->
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner-border spinner-border-custom text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري المعالجة...</p>
            </div>

            <!-- Alerts Container -->
            <div id="alertsContainer"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script>
        // متغيرات عامة
        let socket;
        let selectedLetter = null;
        let arabicLetters = [];

        // إعداد الاتصال
        function initializeConnection() {
            socket = io();
            
            socket.on('connect', function() {
                updateConnectionStatus(true);
                showAlert('تم الاتصال بالخادم بنجاح', 'success');
                loadSystemStatus();
            });
            
            socket.on('disconnect', function() {
                updateConnectionStatus(false);
                showAlert('انقطع الاتصال بالخادم', 'danger');
            });
            
            socket.on('prediction_update', function(data) {
                updatePredictionDisplay(data.letter, data.confidence);
            });
            
            socket.on('data_collection_complete', function(data) {
                showAlert(`تم جمع ${data.samples_collected} عينة للحرف ${data.letter}`, 'success');
                hideLoading();
                loadSystemStatus();
            });
        }

        // تحديث حالة الاتصال
        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionStatusText');
            
            if (connected) {
                statusElement.className = 'connection-status connected';
                statusElement.innerHTML = '<i class="fas fa-circle"></i> متصل';
                statusText.textContent = 'متصل';
            } else {
                statusElement.className = 'connection-status disconnected';
                statusElement.innerHTML = '<i class="fas fa-circle"></i> غير متصل';
                statusText.textContent = 'غير متصل';
            }
        }

        // تحميل الحروف العربية
        async function loadArabicLetters() {
            try {
                const response = await fetch('/api/letters');
                arabicLetters = await response.json();
                renderLettersGrid();
            } catch (error) {
                showAlert('خطأ في تحميل الحروف العربية', 'danger');
            }
        }

        // عرض شبكة الحروف
        function renderLettersGrid() {
            const grid = document.getElementById('lettersGrid');
            grid.innerHTML = '';
            
            arabicLetters.forEach(letter => {
                const letterBtn = document.createElement('div');
                letterBtn.className = 'letter-btn';
                letterBtn.textContent = letter;
                letterBtn.onclick = () => selectLetter(letter);
                grid.appendChild(letterBtn);
            });
        }

        // اختيار حرف
        function selectLetter(letter) {
            selectedLetter = letter;
            
            // إزالة التحديد من جميع الأزرار
            document.querySelectorAll('.letter-btn').forEach(btn => {
                btn.classList.remove('selected');
            });
            
            // إضافة التحديد للحرف المختار
            event.target.classList.add('selected');
            
            showAlert(`تم اختيار الحرف: ${letter}`, 'info');
        }

        // بدء جمع البيانات
        async function startDataCollection() {
            if (!selectedLetter) {
                showAlert('يرجى اختيار حرف أولاً', 'warning');
                return;
            }
            
            const numSamples = document.getElementById('numSamples').value;
            
            showLoading();
            
            try {
                const response = await fetch('/api/collect_data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        letter: selectedLetter,
                        num_samples: parseInt(numSamples)
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showAlert(result.message, 'success');
                } else {
                    showAlert(result.error, 'danger');
                    hideLoading();
                }
            } catch (error) {
                showAlert('خطأ في بدء جمع البيانات', 'danger');
                hideLoading();
            }
        }

        // إيقاف جمع البيانات
        function stopDataCollection() {
            showAlert('تم إيقاف جمع البيانات', 'info');
            hideLoading();
        }

        // تدريب النموذج
        async function trainModel() {
            showLoading();
            
            try {
                const response = await fetch('/api/train_model', {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showAlert(result.message, 'success');
                    loadSystemStatus();
                } else {
                    showAlert(result.error, 'danger');
                }
            } catch (error) {
                showAlert('خطأ في تدريب النموذج', 'danger');
            }
            
            hideLoading();
        }

        // بدء التعرف
        async function startRecognition() {
            try {
                const response = await fetch('/api/start_recognition', {
                    method: 'POST'
                });
                
                const result = await response.json();
                showAlert(result.message, 'success');
                loadSystemStatus();
            } catch (error) {
                showAlert('خطأ في بدء التعرف', 'danger');
            }
        }

        // إيقاف التعرف
        async function stopRecognition() {
            try {
                const response = await fetch('/api/stop_recognition', {
                    method: 'POST'
                });
                
                const result = await response.json();
                showAlert(result.message, 'info');
                loadSystemStatus();
                
                // إعادة تعيين عرض التنبؤ
                updatePredictionDisplay('-', 'في انتظار التعرف...');
            } catch (error) {
                showAlert('خطأ في إيقاف التعرف', 'danger');
            }
        }

        // تحميل حالة النظام
        async function loadSystemStatus() {
            try {
                const response = await fetch('/api/model_status');
                const status = await response.json();
                
                document.getElementById('modelStatus').textContent = status.model_trained ? 'مدرب' : 'غير مدرب';
                document.getElementById('collectingStatus').textContent = status.is_collecting ? 'نشط' : 'متوقف';
                document.getElementById('recognitionStatus').textContent = status.recognition_active ? 'نشط' : 'متوقف';
                
                if (status.last_prediction && status.recognition_active) {
                    updatePredictionDisplay(status.last_prediction, status.prediction_confidence);
                }
            } catch (error) {
                console.error('خطأ في تحميل حالة النظام:', error);
            }
        }

        // تحديث عرض التنبؤ
        function updatePredictionDisplay(letter, confidence) {
            document.getElementById('predictionLetter').textContent = letter;
            
            if (typeof confidence === 'number') {
                const percentage = (confidence * 100).toFixed(1);
                document.getElementById('predictionConfidence').textContent = `الثقة: ${percentage}%`;
            } else {
                document.getElementById('predictionConfidence').textContent = confidence;
            }
        }

        // عرض التنبيهات
        function showAlert(message, type) {
            const alertsContainer = document.getElementById('alertsContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-custom alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            alertsContainer.appendChild(alertDiv);
            
            // إزالة التنبيه بعد 5 ثوان
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // عرض شاشة التحميل
        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'block';
        }

        // إخفاء شاشة التحميل
        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
        }

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            initializeConnection();
            loadArabicLetters();
            
            // تحديث حالة النظام كل 5 ثوان
            setInterval(loadSystemStatus, 5000);
        });
    </script>
</body>
</html>
