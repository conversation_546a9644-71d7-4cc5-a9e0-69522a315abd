# تقرير شامل عن تطبيق لغة الإشارة

## مقدمة

هذا التقرير يقدم تحليلاً شاملاً لتطبيق لغة الإشارة، مع التركيز على ملف `API.py` الذي يمثل العمود الفقري للتطبيق. يتضمن التقرير شرحاً مفصلاً للمكتبات المستخدمة، وتحليلاً لكل جزء من الكود، مع التعمق في تقنيات الذكاء الاصطناعي والتعلم الآلي المستخدمة.

## المكتبات المستخدمة

### 1. مكتبات الويب والخادم

#### Flask وملحقاته
- **Flask**: إطار عمل ويب خفيف الوزن يستخدم لبناء تطبيقات الويب. يوفر أدوات لتوجيه الطلبات، وإدارة الجلسات، وعرض القوالب.
- **Flask-CORS**: ملحق لـ Flask يسمح بمشاركة الموارد عبر المنشأ (CORS)، مما يتيح للتطبيق التفاعل مع واجهات برمجة التطبيقات من مصادر مختلفة.
- **Flask-SocketIO**: ملحق يضيف دعم Socket.IO إلى تطبيق Flask، مما يسمح بالاتصال ثنائي الاتجاه في الوقت الحقيقي بين العميل والخادم.

#### Werkzeug
- مكتبة أساسية تستخدمها Flask لمعالجة الطلبات HTTP، وإدارة الملفات المرفوعة، وتأمين أسماء الملفات.

### 2. مكتبات قواعد البيانات

#### SQLite3
- نظام قاعدة بيانات خفيف الوزن يستخدم لتخزين بيانات المستخدمين، وفيديوهات الإشارات، وصور الإشارات.
- يتم استخدامه لإنشاء وإدارة جداول المستخدمين والفيديوهات والصور.

### 3. مكتبات الأمان والمصادقة

#### bcrypt
- مكتبة لتشفير كلمات المرور بشكل آمن باستخدام خوارزمية bcrypt.
- تستخدم لتشفير كلمات مرور المستخدمين قبل تخزينها في قاعدة البيانات، وللتحقق من صحة كلمات المرور عند تسجيل الدخول.

#### PyJWT
- مكتبة للتعامل مع توكنات JSON Web Token (JWT).
- تستخدم لإنشاء وفك تشفير توكنات المصادقة للمستخدمين.

### 4. مكتبات معالجة الصور والرؤية الحاسوبية

#### OpenCV (cv2)
- مكتبة مفتوحة المصدر للرؤية الحاسوبية ومعالجة الصور.
- تستخدم لمعالجة صور الإشارات، وتحويل التنسيقات، وتغيير الأحجام، وتحضير الصور للتحليل.

#### NumPy
- مكتبة للحوسبة العلمية في بايثون، توفر دعماً للمصفوفات والعمليات الرياضية عالية الأداء.
- تستخدم لمعالجة البيانات المستخرجة من صور الإشارات وتحويلها إلى تنسيق مناسب للنماذج.

### 5. مكتبات الذكاء الاصطناعي والتعلم الآلي

#### MediaPipe
- مكتبة من Google تقدم حلولاً جاهزة للرؤية الحاسوبية وتتبع الحركة.
- تستخدم لاكتشاف وتتبع اليدين في الصور، واستخراج النقاط المرجعية (landmarks) التي تمثل مواضع المفاصل والأصابع.

#### scikit-learn
- مكتبة للتعلم الآلي في بايثون، توفر أدوات للتصنيف والانحدار والتجميع.
- تستخدم لتحميل وتشغيل نموذج التعلم الآلي المدرب مسبقاً للتعرف على إشارات اليد.

#### TensorFlow (ضمنياً)
- إطار عمل للتعلم الآلي والذكاء الاصطناعي، يستخدم لبناء وتدريب نماذج الشبكات العصبية.
- يستخدم ضمنياً من خلال MediaPipe لتشغيل نماذج اكتشاف اليد.

### 6. مكتبات الاتصال والرسائل

#### Twilio
- خدمة اتصالات سحابية تتيح إرسال الرسائل النصية والمكالمات الصوتية.
- تستخدم لإرسال رسائل نصية تحتوي على رموز OTP للمصادقة وإعادة تعيين كلمات المرور.

#### smtplib وmime
- مكتبات قياسية في بايثون للتعامل مع البريد الإلكتروني.
- تستخدم لإرسال رسائل البريد الإلكتروني للمستخدمين (غير مفعلة حالياً في الكود).

### 7. مكتبات أخرى

#### googletrans
- مكتبة للترجمة بين اللغات باستخدام Google Translate API.
- تستخدم لترجمة النصوص بين العربية والإنجليزية للبحث عن فيديوهات الإشارات المناسبة.

#### base64
- مكتبة قياسية في بايثون للتشفير وفك التشفير بتنسيق Base64.
- تلعب دورًا حيويًا في التطبيق للأغراض التالية:
  - **تحويل الصور المستلمة**: تحويل الصور المشفرة بتنسيق Base64 (المرسلة من العميل) إلى بيانات ثنائية يمكن معالجتها باستخدام OpenCV.
  - **تخزين واسترجاع الصور**: تشفير وفك تشفير الصور المخزنة في قاعدة البيانات.
  - **نقل الصور عبر API**: تسهيل نقل الصور بين العميل والخادم عبر طلبات HTTP، حيث يتم تحويل الصور إلى سلاسل نصية يمكن تضمينها في طلبات JSON.
  - **معالجة إطارات الفيديو**: تحويل إطارات الفيديو المرسلة عبر Socket.IO من تنسيق Base64 إلى مصفوفات NumPy للمعالجة.
  - **تأمين البيانات**: المساعدة في تأمين نقل البيانات الحساسة مثل صور المستخدمين.

#### pickle
- مكتبة قياسية في بايثون لحفظ وتحميل الكائنات.
- تستخدم لتحميل نموذج التعلم الآلي المدرب مسبقاً من ملف.

## تحليل الكود

### 1. إعداد التطبيق والاتصال بقاعدة البيانات

```python
app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "*"}})

# إعداد Flask-SocketIO
socketio = SocketIO(app, cors_allowed_origins="*")

# إعدادات تحميل الملفات
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'uploads', 'videos')
ALLOWED_EXTENSIONS = {'mp4', 'webm', 'mov', 'avi'}
MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 50 ميجابايت كحد أقصى

# التأكد من وجود مجلد التحميل
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# Connect to SQLite database (or create if it doesn't exist)
conn = sqlite3.connect("sign_language.db", check_same_thread=False)
cursor = conn.cursor()
```

- يتم إنشاء تطبيق Flask وتكوينه مع CORS لدعم الطلبات من مصادر مختلفة.
- يتم إعداد Socket.IO للاتصال في الوقت الحقيقي.
- يتم تحديد إعدادات تحميل الملفات، بما في ذلك المجلد والأنواع المسموح بها والحجم الأقصى.
- يتم إنشاء اتصال بقاعدة بيانات SQLite.

### 2. إنشاء جداول قاعدة البيانات

```python
cursor.execute('''
CREATE TABLE IF NOT EXISTS Users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    phone_number TEXT NOT NULL UNIQUE,
    address TEXT,
    user_type TEXT,
    password TEXT NOT NULL,
    otp TEXT
);
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS SignVideos (
    character_id INTEGER PRIMARY KEY AUTOINCREMENT,
    character_name TEXT NOT NULL,
    character_video TEXT NOT NULL
);
''')
```

- يتم إنشاء جدول `Users` لتخزين بيانات المستخدمين، بما في ذلك الاسم ورقم الهاتف والعنوان ونوع المستخدم وكلمة المرور ورمز OTP.
- يتم إنشاء جدول `SignVideos` لتخزين فيديوهات الإشارات، مع ربط كل فيديو باسم الحرف أو الكلمة التي يمثلها.

### 3. تحميل نموذج التعلم الآلي

```python
try:
    model_dict = pickle.load(open('./model.p', 'rb'))
    model = model_dict['model']
    print("Model loaded successfully!")
except Exception as e:
    print(f"Error loading model: {str(e)}")
    model = None

# إعداد Mediapipe
mp_hands = mp.solutions.hands
hands = mp_hands.Hands(static_image_mode=False, max_num_hands=1, min_detection_confidence=0.5, min_tracking_confidence=0.5)
```

- يتم تحميل نموذج التعلم الآلي المدرب مسبقاً من ملف `model.p` باستخدام مكتبة pickle.
- يتم إعداد MediaPipe لاكتشاف وتتبع اليدين في الصور، مع تحديد معلمات مثل عدد اليدين الأقصى ومستويات الثقة.

### 4. تعريف قواميس الإشارات

```python
labels_dict_ar = {
    0: 'أ', 1: 'ب', 2: 'ت', 3: 'ث', 4: 'ج', 5: 'ح', 6: 'خ', 7: 'د', 8: 'ذ', 9: 'ر',
    # ... المزيد من الإشارات العربية
}

labels_dict_en = {
    0: 'A', 1: 'B', 2: 'C', 3: 'D', 4: 'E', 5: 'F', 6: 'G', 7: 'H', 8: 'I', 9: 'J',
    # ... المزيد من الإشارات الإنجليزية
}
```

- يتم تعريف قواميس لربط مخرجات النموذج (الأرقام) بالحروف والكلمات المقابلة في اللغتين العربية والإنجليزية.

### 5. نقاط النهاية الرئيسية (API Endpoints)

#### 5.1 الصفحة الرئيسية

```python
@app.route('/')
def index():
    return render_template('test_endpoints.html')
```

- يعرض الصفحة الرئيسية للتطبيق.

#### 5.2 تسجيل المستخدمين وتسجيل الدخول

```python
@app.route('/register', methods=['POST'])
def register():
    # ... كود تسجيل المستخدم
    
@app.route('/login', methods=['POST'])
def login():
    # ... كود تسجيل الدخول
```

- تتيح للمستخدمين إنشاء حسابات جديدة وتسجيل الدخول.
- تستخدم bcrypt لتشفير كلمات المرور والتحقق منها.
- تستخدم JWT لإنشاء توكنات المصادقة.

#### 5.3 إعادة تعيين كلمة المرور

```python
@app.route('/forgot_password', methods=['POST'])
def forgot_password():
    # ... كود إرسال رمز OTP
    
@app.route('/verify_otp', methods=['POST'])
def verify_otp():
    # ... كود التحقق من رمز OTP وتعيين كلمة مرور جديدة
```

- تتيح للمستخدمين إعادة تعيين كلمات المرور المنسية.
- تستخدم Twilio لإرسال رموز OTP عبر الرسائل النصية.

#### 5.4 التعرف على الإشارات

```python
@app.route('/detect_sign', methods=['POST'])
def detect_sign():
    # ... كود التعرف على الإشارات
```

- تتلقى صورة من المستخدم وتحللها للتعرف على الإشارة.
- تستخدم MediaPipe لاكتشاف اليد واستخراج النقاط المرجعية.
- تستخدم نموذج التعلم الآلي للتنبؤ بالإشارة.
- تعيد الإشارة المكتشفة وفيديو مرتبط إن وجد.

#### 5.5 إدارة فيديوهات الإشارات

```python
@app.route('/get_videos', methods=['GET'])
def get_videos():
    # ... كود الحصول على الفيديوهات
    
@app.route('/add_video', methods=['POST'])
def add_video():
    # ... كود إضافة فيديو جديد
```

- تتيح للمستخدمين الحصول على فيديوهات الإشارات وإضافة فيديوهات جديدة.

#### 5.6 البحث عن فيديوهات الإشارات

```python
@app.route('/get_sign_images', methods=['POST'])
def get_sign_images():
    # ... كود البحث عن فيديوهات الإشارات
```

- تتلقى نصاً وتعيد فيديوهات الإشارات المقابلة.
- تستخدم googletrans للترجمة بين اللغات إذا لزم الأمر.

### 6. وظائف Socket.IO للتعرف المستمر

```python
@socketio.on('video_frame')
def handle_video_frame(data):
    # ... كود معالجة إطارات الفيديو والتعرف على الإشارات
```

- تتلقى إطارات فيديو من العميل وتحللها في الوقت الحقيقي.
- تستخدم نفس تقنيات التعرف على الإشارات المستخدمة في `detect_sign`.
- ترسل النتائج مباشرة إلى العميل عبر Socket.IO.

### 7. وظائف مساعدة

```python
def send_sms(phone_number, message):
    # ... كود إرسال الرسائل النصية
    
def compare_images(image1, image2):
    # ... كود مقارنة الصور
```

- وظائف مساعدة لإرسال الرسائل النصية ومقارنة الصور.

## تحليل معمق لجزء الذكاء الاصطناعي

### 1. تقنيات معالجة اللغات الطبيعية (NLP)

على الرغم من أن التطبيق لا يستخدم تقنيات NLP بشكل مباشر، إلا أنه يتعامل مع اللغة من خلال:

- **الترجمة بين اللغات**: يستخدم مكتبة googletrans للترجمة بين العربية والإنجليزية.
- **معالجة النصوص**: يقوم بتحويل النصوص إلى أحرف كبيرة وتقسيمها إلى أحرف فردية للبحث عن فيديوهات الإشارات المقابلة.

### 2. تقنيات الرؤية الحاسوبية (Computer Vision)

يستخدم التطبيق تقنيات متقدمة في الرؤية الحاسوبية من خلال:

- **MediaPipe**: يستخدم نموذج Hands من MediaPipe، وهو نموذج عميق مبني على TensorFlow لاكتشاف وتتبع اليدين في الصور.
- **استخراج النقاط المرجعية**: يستخرج 21 نقطة مرجعية من كل يد، تمثل مواضع المفاصل والأصابع.
- **معالجة الصور**: يستخدم OpenCV لتحويل الصور بين الفضاءات اللونية (RGB و BGR) وتغيير أحجامها.

### 3. تقنيات التعلم الآلي (Machine Learning)

يعتمد التطبيق على نموذج تعلم آلي مدرب مسبقاً للتعرف على إشارات اليد:

- **نموذج التصنيف**: يستخدم نموذجاً من scikit-learn (على الأرجح RandomForest أو SVM) للتصنيف.
- **معالجة البيانات**: يقوم بتطبيع البيانات المستخرجة من النقاط المرجعية لليد قبل تمريرها إلى النموذج.
- **التنبؤ**: يستخدم النموذج للتنبؤ بالإشارة المقابلة للصورة، مع درجة ثقة.

### 4. تقنيات TensorFlow (ضمنياً)

يستخدم التطبيق TensorFlow بشكل ضمني من خلال:

- **MediaPipe**: تعتمد نماذج MediaPipe على TensorFlow وتستخدم شبكات عصبية عميقة.
- **نماذج الكشف**: يستخدم نموذج Palm Detection لتحديد موقع اليد في الصورة.
- **نماذج التتبع**: يستخدم نموذج Hand Landmark لتحديد النقاط المرجعية على اليد.

### 5. خوارزمية التعرف على الإشارات

تتكون خوارزمية التعرف على الإشارات من الخطوات التالية:

1. **استقبال الصورة**: يتلقى التطبيق صورة من المستخدم (Base64).
2. **فك ترميز الصورة**: يحول الصورة من Base64 إلى مصفوفة NumPy.
3. **اكتشاف اليد**: يستخدم MediaPipe لاكتشاف اليد في الصورة.
4. **استخراج النقاط المرجعية**: يستخرج 21 نقطة مرجعية من اليد.
5. **تطبيع البيانات**: يطبع البيانات بطرح الحد الأدنى من الإحداثيات.
6. **التنبؤ**: يستخدم نموذج التعلم الآلي للتنبؤ بالإشارة.
7. **تحديد اللغة**: يختار القاموس المناسب (عربي أو إنجليزي) بناءً على اللغة المحددة.
8. **البحث عن الفيديو**: يبحث عن فيديو مرتبط بالإشارة المكتشفة.
9. **إرجاع النتيجة**: يعيد الإشارة المكتشفة والفيديو المرتبط ودرجة الثقة.

## الخلاصة

يمثل تطبيق لغة الإشارة نموذجاً متكاملاً يجمع بين تقنيات الويب، وقواعد البيانات، والأمان، والذكاء الاصطناعي، والتعلم الآلي، والرؤية الحاسوبية. يستخدم التطبيق مجموعة متنوعة من المكتبات والتقنيات لتوفير تجربة سلسة للمستخدمين، مع التركيز على دقة التعرف على الإشارات وسهولة الاستخدام.

يعتمد جزء الذكاء الاصطناعي في التطبيق على تقنيات متقدمة في الرؤية الحاسوبية والتعلم الآلي، مع استخدام ضمني لـ TensorFlow من خلال MediaPipe. يمكن تحسين التطبيق مستقبلاً من خلال استخدام نماذج أكثر تقدماً مثل الشبكات العصبية العميقة، وتحسين دقة التعرف على الإشارات، وإضافة دعم للمزيد من اللغات.
