#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نموذج التعرف على لغة الإشارة العربية
Arabic Sign Language Recognition Model
"""

import cv2
import numpy as np
import mediapipe as mp
import pickle
import pandas as pd
from flask import Flask, render_template, jsonify, request
from flask_socketio import SocketIO, emit
import base64
import io
from PIL import Image
import threading
import time

# إعداد Flask و SocketIO
app = Flask(__name__)
app.config['SECRET_KEY'] = 'arabic_sign_language_secret'
socketio = SocketIO(app, cors_allowed_origins="*")

# إعداد MediaPipe
mp_hands = mp.solutions.hands
mp_drawing = mp.solutions.drawing_utils
mp_drawing_styles = mp.solutions.drawing_styles

# الحروف العربية
ARABIC_LETTERS = [
    'أ', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص', 'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'
]

class ArabicSignLanguageModel:
    def __init__(self):
        self.hands = mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=1,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.model = None
        self.is_collecting = False
        self.current_letter = None
        self.collected_data = []
        self.recognition_active = False
        self.last_prediction = ""
        self.prediction_confidence = 0.0
        
        # محاولة تحميل النموذج المدرب مسبقاً
        try:
            with open('arabic_sign_model.pkl', 'rb') as f:
                self.model = pickle.load(f)
            print("✅ تم تحميل النموذج المدرب مسبقاً")
        except FileNotFoundError:
            print("⚠️ لم يتم العثور على نموذج مدرب، سيتم إنشاء نموذج جديد")
            self.model = None

    def extract_landmarks(self, image):
        """استخراج النقاط المرجعية من الصورة"""
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        results = self.hands.process(image_rgb)
        
        if results.multi_hand_landmarks:
            landmarks = []
            for hand_landmarks in results.multi_hand_landmarks:
                for landmark in hand_landmarks.landmark:
                    landmarks.extend([landmark.x, landmark.y, landmark.z])
            return np.array(landmarks)
        return None

    def collect_data_for_letter(self, letter, num_samples=100):
        """جمع البيانات لحرف معين"""
        self.current_letter = letter
        self.is_collecting = True
        self.collected_data = []
        
        print(f"🔄 بدء جمع البيانات للحرف: {letter}")
        print(f"📊 المطلوب جمع {num_samples} عينة")
        
        cap = cv2.VideoCapture(0)
        sample_count = 0
        
        while sample_count < num_samples and self.is_collecting:
            ret, frame = cap.read()
            if not ret:
                continue
                
            # عكس الصورة للحصول على تأثير المرآة
            frame = cv2.flip(frame, 1)
            
            # استخراج النقاط المرجعية
            landmarks = self.extract_landmarks(frame)
            
            if landmarks is not None:
                self.collected_data.append({
                    'landmarks': landmarks,
                    'letter': letter
                })
                sample_count += 1
                
                # رسم النقاط المرجعية
                image_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                results = self.hands.process(image_rgb)
                
                if results.multi_hand_landmarks:
                    for hand_landmarks in results.multi_hand_landmarks:
                        mp_drawing.draw_landmarks(
                            frame, hand_landmarks, mp_hands.HAND_CONNECTIONS,
                            mp_drawing_styles.get_default_hand_landmarks_style(),
                            mp_drawing_styles.get_default_hand_connections_style()
                        )
            
            # عرض المعلومات على الشاشة
            cv2.putText(frame, f"Letter: {letter}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, f"Samples: {sample_count}/{num_samples}", (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, "Press 'q' to stop", (10, 110), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            
            cv2.imshow('Arabic Sign Language Data Collection', frame)
            
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
                
        cap.release()
        cv2.destroyAllWindows()
        self.is_collecting = False
        
        print(f"✅ تم جمع {len(self.collected_data)} عينة للحرف {letter}")
        return self.collected_data

    def train_model(self):
        """تدريب النموذج على البيانات المجمعة"""
        if not self.collected_data:
            print("❌ لا توجد بيانات للتدريب")
            return False
            
        # تحضير البيانات
        X = []
        y = []
        
        for data in self.collected_data:
            X.append(data['landmarks'])
            y.append(data['letter'])
        
        X = np.array(X)
        y = np.array(y)
        
        # تدريب نموذج بسيط (يمكن استبداله بنموذج أكثر تعقيداً)
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score
        
        # تقسيم البيانات
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # تدريب النموذج
        self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.model.fit(X_train, y_train)
        
        # تقييم النموذج
        y_pred = self.model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"✅ تم تدريب النموذج بدقة: {accuracy:.2%}")
        
        # حفظ النموذج
        with open('arabic_sign_model.pkl', 'wb') as f:
            pickle.dump(self.model, f)
        
        print("💾 تم حفظ النموذج في arabic_sign_model.pkl")
        return True

    def predict_sign(self, image):
        """التنبؤ بالإشارة من الصورة"""
        if self.model is None:
            return None, 0.0
            
        landmarks = self.extract_landmarks(image)
        if landmarks is not None:
            prediction = self.model.predict([landmarks])
            probabilities = self.model.predict_proba([landmarks])
            confidence = np.max(probabilities)
            
            return prediction[0], confidence
        
        return None, 0.0

    def start_recognition(self):
        """بدء التعرف على الإشارات في الوقت الفعلي"""
        self.recognition_active = True
        
        def recognition_loop():
            cap = cv2.VideoCapture(0)
            
            while self.recognition_active:
                ret, frame = cap.read()
                if not ret:
                    continue
                    
                # عكس الصورة للحصول على تأثير المرآة
                frame = cv2.flip(frame, 1)
                
                # التنبؤ بالإشارة
                prediction, confidence = self.predict_sign(frame)
                
                if prediction and confidence > 0.7:
                    self.last_prediction = prediction
                    self.prediction_confidence = confidence
                    
                    # إرسال النتيجة عبر SocketIO
                    socketio.emit('prediction_update', {
                        'letter': prediction,
                        'confidence': float(confidence),
                        'timestamp': time.time()
                    })
                
                # رسم النقاط المرجعية
                image_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                results = self.hands.process(image_rgb)
                
                if results.multi_hand_landmarks:
                    for hand_landmarks in results.multi_hand_landmarks:
                        mp_drawing.draw_landmarks(
                            frame, hand_landmarks, mp_hands.HAND_CONNECTIONS,
                            mp_drawing_styles.get_default_hand_landmarks_style(),
                            mp_drawing_styles.get_default_hand_connections_style()
                        )
                
                # عرض النتيجة على الشاشة
                if prediction:
                    cv2.putText(frame, f"Letter: {prediction}", (10, 30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                    cv2.putText(frame, f"Confidence: {confidence:.2%}", (10, 70), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                
                cv2.putText(frame, "Press 'q' to stop", (10, 110), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                
                cv2.imshow('Arabic Sign Language Recognition', frame)
                
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
                    
            cap.release()
            cv2.destroyAllWindows()
        
        # تشغيل التعرف في thread منفصل
        recognition_thread = threading.Thread(target=recognition_loop)
        recognition_thread.daemon = True
        recognition_thread.start()

    def stop_recognition(self):
        """إيقاف التعرف على الإشارات"""
        self.recognition_active = False

# إنشاء مثيل من النموذج
arabic_model = ArabicSignLanguageModel()

# Routes للواجهة الويب
@app.route('/')
def index():
    return render_template('arabic_sign_recognition.html')

@app.route('/api/letters')
def get_letters():
    """الحصول على قائمة الحروف العربية"""
    return jsonify(ARABIC_LETTERS)

@app.route('/api/collect_data', methods=['POST'])
def collect_data():
    """بدء جمع البيانات لحرف معين"""
    data = request.json
    letter = data.get('letter')
    num_samples = data.get('num_samples', 100)
    
    if letter not in ARABIC_LETTERS:
        return jsonify({'error': 'حرف غير صحيح'}), 400
    
    # تشغيل جمع البيانات في thread منفصل
    def collect_thread():
        arabic_model.collect_data_for_letter(letter, num_samples)
        socketio.emit('data_collection_complete', {
            'letter': letter,
            'samples_collected': len(arabic_model.collected_data)
        })
    
    thread = threading.Thread(target=collect_thread)
    thread.daemon = True
    thread.start()
    
    return jsonify({'message': f'بدء جمع البيانات للحرف {letter}'})

@app.route('/api/train_model', methods=['POST'])
def train_model():
    """تدريب النموذج"""
    success = arabic_model.train_model()
    if success:
        return jsonify({'message': 'تم تدريب النموذج بنجاح'})
    else:
        return jsonify({'error': 'فشل في تدريب النموذج'}), 400

@app.route('/api/start_recognition', methods=['POST'])
def start_recognition():
    """بدء التعرف على الإشارات"""
    arabic_model.start_recognition()
    return jsonify({'message': 'تم بدء التعرف على الإشارات'})

@app.route('/api/stop_recognition', methods=['POST'])
def stop_recognition():
    """إيقاف التعرف على الإشارات"""
    arabic_model.stop_recognition()
    return jsonify({'message': 'تم إيقاف التعرف على الإشارات'})

@app.route('/api/model_status')
def model_status():
    """حالة النموذج"""
    return jsonify({
        'model_trained': arabic_model.model is not None,
        'is_collecting': arabic_model.is_collecting,
        'recognition_active': arabic_model.recognition_active,
        'last_prediction': arabic_model.last_prediction,
        'prediction_confidence': arabic_model.prediction_confidence
    })

# SocketIO Events
@socketio.on('connect')
def handle_connect():
    print('👤 مستخدم جديد متصل')
    emit('connected', {'message': 'تم الاتصال بنجاح'})

@socketio.on('disconnect')
def handle_disconnect():
    print('👤 مستخدم منقطع')

if __name__ == '__main__':
    print("🚀 بدء تشغيل خادم التعرف على لغة الإشارة العربية")
    print("🌐 افتح المتصفح على: http://localhost:5000")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
